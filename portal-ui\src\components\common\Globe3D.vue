<template>
  <div class="globe-container" ref="globeContainer">
    <div class="globe-canvas" ref="canvasContainer"></div>
    <div class="loading-indicator" v-if="isLoading">
      <div class="loading-spinner"></div>
      <div class="loading-text">正在加载全球算力网络...</div>
    </div>
    <div class="globe-overlay" v-if="!isLoading">
      <div class="globe-title">全球算力布局</div>
      <div class="globe-subtitle">分布式高性能计算网络</div>
      <div class="interaction-hint">拖拽旋转 | 点击节点查看详情</div>
      <div class="node-info" v-if="selectedNode" :style="nodeInfoStyle">
        <h4>{{ selectedNode.name }}</h4>
        <p>{{ selectedNode.description }}</p>
        <div class="node-stats">
          <span>GPU节点: {{ selectedNode.gpuCount }}</span>
          <span>算力: {{ selectedNode.computePower }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as THREE from 'three'

export default {
  name: 'Globe3D',
  data() {
    return {
      scene: null,
      camera: null,
      renderer: null,
      globe: null,
      nodes: [],
      selectedNode: null,
      mousePosition: { x: 0, y: 0 },
      animationId: null,
      isDragging: false,
      previousMousePosition: { x: 0, y: 0 },
      rotationSpeed: { x: 0, y: 0.005 },
      isLoading: true,
      // 全球算力节点数据
      computeNodes: [
        {
          name: '北京节点',
          lat: 39.9042,
          lng: 116.4074,
          description: '华北地区主要算力中心',
          gpuCount: '2000+',
          computePower: '500 PFLOPS'
        },
        {
          name: '上海节点',
          lat: 31.2304,
          lng: 121.4737,
          description: '华东地区核心算力枢纽',
          gpuCount: '1800+',
          computePower: '450 PFLOPS'
        },
        {
          name: '深圳节点',
          lat: 22.3193,
          lng: 114.1694,
          description: '华南地区智算中心',
          gpuCount: '1500+',
          computePower: '380 PFLOPS'
        },
        {
          name: '成都节点',
          lat: 30.5728,
          lng: 104.0668,
          description: '西南地区算力基地',
          gpuCount: '1200+',
          computePower: '300 PFLOPS'
        },
        {
          name: '杭州节点',
          lat: 30.2741,
          lng: 120.1551,
          description: '长三角算力集群',
          gpuCount: '1000+',
          computePower: '250 PFLOPS'
        },
        {
          name: '新加坡节点',
          lat: 1.3521,
          lng: 103.8198,
          description: '东南亚算力中心',
          gpuCount: '800+',
          computePower: '200 PFLOPS'
        }
      ]
    }
  },
  computed: {
    nodeInfoStyle() {
      return {
        left: this.mousePosition.x + 20 + 'px',
        top: this.mousePosition.y - 50 + 'px'
      }
    }
  },
  mounted() {
    this.initThree()
    this.createGlobe()
    this.createNodes()
    this.createStars()
    this.animate()
    this.addEventListeners()

    // 延迟隐藏加载指示器
    setTimeout(() => {
      this.isLoading = false
    }, 1500)
  },
  beforeDestroy() {
    this.cleanup()
  },
  methods: {
    initThree() {
      // 创建场景
      this.scene = new THREE.Scene()
      
      // 创建相机
      const container = this.$refs.canvasContainer
      const width = container.clientWidth
      const height = container.clientHeight
      
      this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000)
      this.camera.position.z = 3
      
      // 创建渲染器
      this.renderer = new THREE.WebGLRenderer({ 
        antialias: true, 
        alpha: true 
      })
      this.renderer.setSize(width, height)
      this.renderer.setClearColor(0x000000, 0)
      container.appendChild(this.renderer.domElement)
      
      // 添加环境光
      const ambientLight = new THREE.AmbientLight(0x404040, 0.6)
      this.scene.add(ambientLight)
      
      // 添加方向光
      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
      directionalLight.position.set(1, 1, 1)
      this.scene.add(directionalLight)
    },
    
    createGlobe() {
      // 创建地球几何体
      const geometry = new THREE.SphereGeometry(1, 64, 64)

      // 创建地球材质 - 使用更真实的地球效果
      const material = new THREE.MeshPhongMaterial({
        color: 0x2E86AB,
        transparent: true,
        opacity: 0.9,
        shininess: 100,
        wireframe: false
      })

      this.globe = new THREE.Mesh(geometry, material)
      this.scene.add(this.globe)

      // 添加地球轮廓线 - 经纬线效果
      const wireframeGeometry = new THREE.SphereGeometry(1.005, 32, 16)
      const wireframeMaterial = new THREE.MeshBasicMaterial({
        color: 0x1470FF,
        wireframe: true,
        transparent: true,
        opacity: 0.4
      })
      const wireframe = new THREE.Mesh(wireframeGeometry, wireframeMaterial)
      this.scene.add(wireframe)

      // 添加大气层效果
      const atmosphereGeometry = new THREE.SphereGeometry(1.1, 32, 32)
      const atmosphereMaterial = new THREE.MeshBasicMaterial({
        color: 0x4A90FF,
        transparent: true,
        opacity: 0.1,
        side: THREE.BackSide
      })
      const atmosphere = new THREE.Mesh(atmosphereGeometry, atmosphereMaterial)
      this.scene.add(atmosphere)
    },
    
    createNodes() {
      this.computeNodes.forEach(nodeData => {
        const node = this.createNode(nodeData)
        this.nodes.push(node)
        this.scene.add(node.mesh)
      })

      // 创建节点间的连接线
      this.createConnections()
    },
    
    createNode(nodeData) {
      // 将经纬度转换为3D坐标
      const phi = (90 - nodeData.lat) * (Math.PI / 180)
      const theta = (nodeData.lng + 180) * (Math.PI / 180)
      
      const x = -(1.05 * Math.sin(phi) * Math.cos(theta))
      const y = 1.05 * Math.cos(phi)
      const z = 1.05 * Math.sin(phi) * Math.sin(theta)
      
      // 创建节点几何体
      const geometry = new THREE.SphereGeometry(0.02, 16, 16)
      const material = new THREE.MeshBasicMaterial({
        color: 0xFFFFFF,
        transparent: true
      })
      
      const mesh = new THREE.Mesh(geometry, material)
      mesh.position.set(x, y, z)
      
      // 创建光环效果
      const ringGeometry = new THREE.RingGeometry(0.03, 0.05, 16)
      const ringMaterial = new THREE.MeshBasicMaterial({
        color: 0x1470FF,
        transparent: true,
        opacity: 0.6,
        side: THREE.DoubleSide
      })
      const ring = new THREE.Mesh(ringGeometry, ringMaterial)
      ring.position.copy(mesh.position)
      ring.lookAt(new THREE.Vector3(0, 0, 0))
      this.scene.add(ring)
      
      return {
        mesh,
        ring,
        data: nodeData,
        position: { x, y, z }
      }
    },

    createConnections() {
      // 创建主要节点间的连接线
      const connections = [
        [0, 1], // 北京-上海
        [1, 2], // 上海-深圳
        [0, 3], // 北京-成都
        [1, 4], // 上海-杭州
        [2, 5]  // 深圳-新加坡
      ]

      connections.forEach(([startIdx, endIdx]) => {
        const startNode = this.nodes[startIdx]
        const endNode = this.nodes[endIdx]

        if (startNode && endNode) {
          const curve = new THREE.QuadraticBezierCurve3(
            new THREE.Vector3(startNode.position.x, startNode.position.y, startNode.position.z),
            new THREE.Vector3(0, 0, 0), // 控制点在地球中心上方
            new THREE.Vector3(endNode.position.x, endNode.position.y, endNode.position.z)
          )

          const points = curve.getPoints(50)
          const geometry = new THREE.BufferGeometry().setFromPoints(points)
          const material = new THREE.LineBasicMaterial({
            color: 0x4A90FF,
            transparent: true,
            opacity: 0.6
          })

          const line = new THREE.Line(geometry, material)
          this.scene.add(line)
        }
      })
    },

    createStars() {
      // 创建星空背景
      const starsGeometry = new THREE.BufferGeometry()
      const starsMaterial = new THREE.PointsMaterial({
        color: 0xFFFFFF,
        size: 2,
        transparent: true,
        opacity: 0.8
      })

      const starsVertices = []
      for (let i = 0; i < 1000; i++) {
        const x = (Math.random() - 0.5) * 2000
        const y = (Math.random() - 0.5) * 2000
        const z = (Math.random() - 0.5) * 2000
        starsVertices.push(x, y, z)
      }

      starsGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starsVertices, 3))
      const stars = new THREE.Points(starsGeometry, starsMaterial)
      this.scene.add(stars)
    },
    
    animate() {
      this.animationId = requestAnimationFrame(this.animate)
      
      // 旋转地球
      if (this.globe) {
        this.globe.rotation.x += this.rotationSpeed.x
        this.globe.rotation.y += this.rotationSpeed.y

        // 逐渐减慢拖拽后的旋转
        this.rotationSpeed.x *= 0.98
        if (!this.isDragging && Math.abs(this.rotationSpeed.y) > 0.005) {
          this.rotationSpeed.y *= 0.98
        } else if (!this.isDragging) {
          this.rotationSpeed.y = 0.005 // 恢复默认旋转速度
        }
      }
      
      // 节点脉冲动画
      this.nodes.forEach((node, index) => {
        const time = Date.now() * 0.001
        const scale = 1 + Math.sin(time * 2 + index) * 0.3
        node.mesh.scale.setScalar(scale)
        
        // 光环旋转
        if (node.ring) {
          node.ring.rotation.z += 0.02
        }
      })
      
      this.renderer.render(this.scene, this.camera)
    },
    
    addEventListeners() {
      window.addEventListener('resize', this.onWindowResize)
      this.renderer.domElement.addEventListener('mousemove', this.onMouseMove)
      this.renderer.domElement.addEventListener('click', this.onMouseClick)
      this.renderer.domElement.addEventListener('mousedown', this.onMouseDown)
      this.renderer.domElement.addEventListener('mouseup', this.onMouseUp)
      this.renderer.domElement.addEventListener('mouseleave', this.onMouseUp)
    },
    
    onWindowResize() {
      const container = this.$refs.canvasContainer
      const width = container.clientWidth
      const height = container.clientHeight
      
      this.camera.aspect = width / height
      this.camera.updateProjectionMatrix()
      this.renderer.setSize(width, height)
    },
    
    onMouseMove(event) {
      this.mousePosition.x = event.clientX
      this.mousePosition.y = event.clientY

      // 处理拖拽旋转
      if (this.isDragging) {
        const deltaX = event.clientX - this.previousMousePosition.x
        const deltaY = event.clientY - this.previousMousePosition.y

        this.rotationSpeed.y = deltaX * 0.01
        this.rotationSpeed.x = -deltaY * 0.01

        this.previousMousePosition.x = event.clientX
        this.previousMousePosition.y = event.clientY
        return
      }

      // 射线检测
      const mouse = new THREE.Vector2()
      const rect = this.renderer.domElement.getBoundingClientRect()
      mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
      mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

      const raycaster = new THREE.Raycaster()
      raycaster.setFromCamera(mouse, this.camera)

      const intersects = raycaster.intersectObjects(this.nodes.map(n => n.mesh))

      if (intersects.length > 0) {
        const intersectedNode = this.nodes.find(n => n.mesh === intersects[0].object)
        this.selectedNode = intersectedNode ? intersectedNode.data : null
        this.renderer.domElement.style.cursor = 'pointer'
      } else {
        this.selectedNode = null
        this.renderer.domElement.style.cursor = this.isDragging ? 'grabbing' : 'grab'
      }
    },
    
    onMouseClick() {
      if (this.selectedNode && !this.isDragging) {
        this.$emit('node-selected', this.selectedNode)
      }
    },

    onMouseDown(event) {
      this.isDragging = true
      this.previousMousePosition.x = event.clientX
      this.previousMousePosition.y = event.clientY
      this.renderer.domElement.style.cursor = 'grabbing'
    },

    onMouseUp() {
      this.isDragging = false
      this.renderer.domElement.style.cursor = 'grab'
    },
    
    cleanup() {
      if (this.animationId) {
        cancelAnimationFrame(this.animationId)
      }
      
      window.removeEventListener('resize', this.onWindowResize)
      
      if (this.renderer) {
        this.renderer.dispose()
      }
    }
  }
}
</script>

<style scoped>
.globe-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.globe-canvas {
  width: 100%;
  height: 100%;
  cursor: grab;
}

.globe-canvas:active {
  cursor: grabbing;
}

.globe-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: white;
}

.globe-title {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.globe-subtitle {
  font-size: 18px;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
  margin-bottom: 20px;
}

.interaction-hint {
  font-size: 14px;
  opacity: 0.7;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

.loading-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(20, 112, 255, 0.1);
  backdrop-filter: blur(5px);
  color: white;
  z-index: 10;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 16px;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.node-info {
  position: fixed;
  background: rgba(20, 112, 255, 0.95);
  color: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.3);
  pointer-events: none;
  z-index: 1000;
  min-width: 200px;
  backdrop-filter: blur(10px);
}

.node-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.node-info p {
  margin: 0 0 10px 0;
  font-size: 14px;
  opacity: 0.9;
}

.node-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.node-stats span {
  font-size: 12px;
  opacity: 0.8;
}

@media (max-width: 768px) {
  .globe-title {
    font-size: 24px;
  }

  .globe-subtitle {
    font-size: 16px;
    margin-bottom: 15px;
  }

  .interaction-hint {
    font-size: 12px;
  }

  .node-info {
    min-width: 160px;
    padding: 10px;
    font-size: 12px;
  }

  .node-info h4 {
    font-size: 14px;
  }

  .loading-text {
    font-size: 14px;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    margin-bottom: 15px;
  }
}
</style>
