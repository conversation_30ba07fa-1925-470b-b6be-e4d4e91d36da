# Globe3D 组件使用说明

## 概述
Globe3D 是一个基于 Three.js 的 3D 地球组件，用于展示全球算力布局。该组件提供了交互式的 3D 地球视图，包含算力节点、连接线和星空背景效果。

## 功能特性

### 🌍 3D 地球渲染
- 真实的地球球体渲染
- 经纬线网格显示
- 大气层效果
- 星空背景

### 📍 算力节点展示
- 全球主要算力节点标记
- 节点脉冲动画效果
- 光环旋转效果
- 鼠标悬停信息显示

### 🔗 节点连接
- 主要节点间的连接线
- 弧形连接路径
- 半透明连接效果

### 🎮 交互功能
- 鼠标拖拽旋转地球
- 节点点击事件
- 自动旋转功能
- 响应式设计

## 使用方法

### 基本用法
```vue
<template>
  <div class="banner">
    <Globe3D @node-selected="onNodeSelected" />
  </div>
</template>

<script>
import Globe3D from '@/components/common/Globe3D'

export default {
  components: {
    Globe3D
  },
  methods: {
    onNodeSelected(nodeData) {
      console.log('选中节点:', nodeData)
      // 处理节点选择逻辑
    }
  }
}
</script>
```

### 事件
- `node-selected`: 当用户点击节点时触发，返回节点数据

### 节点数据结构
```javascript
{
  name: '节点名称',
  lat: 纬度,
  lng: 经度,
  description: '节点描述',
  gpuCount: 'GPU数量',
  computePower: '算力'
}
```

## 样式定制

### 容器要求
组件需要一个有明确高度的容器：
```css
.banner {
  height: 600px;
  position: relative;
}
```

### 主题色
组件使用项目主题色 `#1470FF`，可通过修改组件内部样式进行定制。

## 性能优化

### 建议
1. 确保容器有合适的尺寸
2. 避免频繁的组件重新渲染
3. 在组件销毁时会自动清理 Three.js 资源

### 兼容性
- 支持现代浏览器
- 需要 WebGL 支持
- 移动端优化

## 技术依赖
- Three.js
- Vue 2.x
- WebGL

## 注意事项
1. 组件会在 mounted 时初始化 Three.js 场景
2. 在 beforeDestroy 时自动清理资源
3. 支持窗口大小变化的响应式调整
4. 移动端会显示简化的交互提示

## 自定义节点数据
可以通过修改组件内的 `computeNodes` 数组来添加或修改节点：

```javascript
computeNodes: [
  {
    name: '新节点',
    lat: 40.7128,
    lng: -74.0060,
    description: '节点描述',
    gpuCount: '1000+',
    computePower: '200 PFLOPS'
  }
  // ... 更多节点
]
```

## 故障排除

### 常见问题
1. **地球不显示**: 检查容器高度是否设置
2. **性能问题**: 减少节点数量或降低渲染质量
3. **移动端显示异常**: 检查响应式样式

### 调试
组件提供了控制台日志输出，可以通过浏览器开发者工具查看。
